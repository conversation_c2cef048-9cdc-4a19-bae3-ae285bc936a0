// Modern Navbar Stimulus Controller
// Handles scroll effects, mobile menu, and interactive behaviors

import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["background", "mobileMenu", "mobileButton", "mobileIcon", "mobileCloseIcon"]
  static values = { scrolled: <PERSON>ole<PERSON> }

  connect() {
    this.handleScroll = this.handleScroll.bind(this)
    window.addEventListener('scroll', this.handleScroll)
    this.handleScroll() // Initialize state
  }

  disconnect() {
    window.removeEventListener('scroll', this.handleScroll)
  }

  handleScroll() {
    const scrolled = window.scrollY > 20
    
    if (scrolled !== this.scrolledValue) {
      this.scrolledValue = scrolled
      this.updateNavbarAppearance(scrolled)
    }
  }

  updateNavbarAppearance(scrolled) {
    if (this.hasBackgroundTarget) {
      const background = this.backgroundTarget
      
      if (scrolled) {
        // Scrolled state - more opaque, subtle shadow
        background.classList.remove('bg-white/80', 'shadow-lg')
        background.classList.add('bg-white/95', 'shadow-xl', 'border-gray-300/30')
      } else {
        // Top state - more transparent, lighter shadow
        background.classList.remove('bg-white/95', 'shadow-xl', 'border-gray-300/30')
        background.classList.add('bg-white/80', 'shadow-lg')
      }
    }
  }

  toggleMobile() {
    const menu = this.mobileMenuTarget
    const button = this.mobileButtonTarget
    const openIcon = this.mobileIconTarget
    const closeIcon = this.mobileCloseIconTarget
    
    const isOpen = !menu.classList.contains('opacity-0')
    
    if (isOpen) {
      // Close menu
      this.closeMobileMenu()
    } else {
      // Open menu
      this.openMobileMenu()
    }
    
    // Update button aria-expanded
    button.setAttribute('aria-expanded', !isOpen)
  }

  openMobileMenu() {
    const menu = this.mobileMenuTarget
    const openIcon = this.mobileIconTarget
    const closeIcon = this.mobileCloseIconTarget
    
    // Show menu with animation
    menu.classList.remove('opacity-0', 'invisible', '-translate-y-4')
    menu.classList.add('opacity-100', 'visible', 'translate-y-0')
    
    // Switch icons
    openIcon.classList.add('hidden')
    closeIcon.classList.remove('hidden')
    
    // Add click outside to close
    document.addEventListener('click', this.handleOutsideClick.bind(this))
    
    // Prevent body scroll
    document.body.style.overflow = 'hidden'
  }

  closeMobileMenu() {
    const menu = this.mobileMenuTarget
    const openIcon = this.mobileIconTarget
    const closeIcon = this.mobileCloseIconTarget
    
    // Hide menu with animation
    menu.classList.add('opacity-0', 'invisible', '-translate-y-4')
    menu.classList.remove('opacity-100', 'visible', 'translate-y-0')
    
    // Switch icons
    closeIcon.classList.add('hidden')
    openIcon.classList.remove('hidden')
    
    // Remove click outside listener
    document.removeEventListener('click', this.handleOutsideClick.bind(this))
    
    // Restore body scroll
    document.body.style.overflow = ''
  }

  handleOutsideClick(event) {
    const menu = this.mobileMenuTarget
    const button = this.mobileButtonTarget
    
    // Close menu if click is outside menu and button
    if (!menu.contains(event.target) && !button.contains(event.target)) {
      this.closeMobileMenu()
      button.setAttribute('aria-expanded', 'false')
    }
  }

  // Handle window resize to close mobile menu on desktop
  handleResize() {
    if (window.innerWidth >= 1024) { // lg breakpoint
      this.closeMobileMenu()
      this.mobileButtonTarget.setAttribute('aria-expanded', 'false')
    }
  }

  // Add smooth scroll to anchor links
  scrollToSection(event) {
    const href = event.currentTarget.getAttribute('href')
    
    if (href && href.startsWith('#')) {
      event.preventDefault()
      const target = document.querySelector(href)
      
      if (target) {
        const offsetTop = target.offsetTop - 100 // Account for fixed navbar
        
        window.scrollTo({
          top: offsetTop,
          behavior: 'smooth'
        })
        
        // Close mobile menu if open
        this.closeMobileMenu()
        this.mobileButtonTarget.setAttribute('aria-expanded', 'false')
      }
    }
  }

  // Add keyboard navigation support
  handleKeyDown(event) {
    if (event.key === 'Escape') {
      this.closeMobileMenu()
      this.mobileButtonTarget.setAttribute('aria-expanded', 'false')
      this.mobileButtonTarget.focus()
    }
  }
}
