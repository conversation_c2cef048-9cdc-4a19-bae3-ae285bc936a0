<%# Modern Glass Morphism Navigation Bar %>
<%# Complete redesign with sophisticated aesthetics and clean functionality %>

<nav class="fixed top-0 w-full z-50 transition-all duration-300" 
     data-controller="modern-navbar" 
     data-modern-navbar-scrolled-value="false"
     role="navigation" 
     aria-label="Main navigation">
  
  <!-- Main Navigation Container -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="relative">
      
      <!-- Glass Morphism Background -->
      <div class="absolute inset-0 bg-white/80 backdrop-blur-xl border border-white/20 rounded-2xl mt-4 shadow-lg shadow-black/5"
           data-modern-navbar-target="background"></div>
      
      <!-- Navigation Content -->
      <div class="relative flex items-center justify-between h-20 px-6">
        
        <!-- Logo Section -->
        <div class="flex items-center space-x-3">
          <%= link_to (user_signed_in? ? dashboard_path : root_path), 
              class: "group flex items-center space-x-3 transition-all duration-200 hover:scale-105",
              'aria-label': 'AI Marketing Hub home' do %>
            
            <!-- Animated Logo -->
            <div class="relative">
              <div class="w-11 h-11 bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500 rounded-2xl flex items-center justify-center shadow-lg shadow-blue-500/25 group-hover:shadow-blue-500/40 transition-all duration-300">
                <svg class="w-6 h-6 text-white transform group-hover:scale-110 transition-transform duration-200" 
                     fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
              </div>
              
              <!-- Pulse Ring Effect -->
              <div class="absolute inset-0 rounded-2xl bg-gradient-to-br from-blue-400 to-purple-400 opacity-0 group-hover:opacity-20 group-hover:animate-ping"></div>
            </div>
            
            <!-- Brand Text -->
            <div class="hidden sm:block">
              <h1 class="text-xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent">
                AI Marketing Hub
              </h1>
              <% if user_signed_in? %>
                <p class="text-xs text-gray-500 leading-none">
                  <%= current_user.tenant&.name || "Dashboard" %>
                </p>
              <% end %>
            </div>
          <% end %>
        </div>
        
        <!-- Center Navigation (Desktop) -->
        <div class="hidden lg:flex items-center space-x-1">
          <% unless user_signed_in? %>
            <!-- Marketing Navigation Pills -->
            <div class="flex items-center space-x-2 bg-gray-50/50 backdrop-blur-sm rounded-2xl p-1 border border-gray-200/50">
              <% [
                   { path: features_path, label: 'Features', icon: 'M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10' },
                   { path: pricing_path, label: 'Pricing', icon: 'M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z' },
                   { path: about_path, label: 'About', icon: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z' },
                   { path: contact_path, label: 'Contact', icon: 'M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z' }
                 ].each do |nav_item| %>
                <%= link_to nav_item[:path], 
                    class: "group relative flex items-center space-x-2 px-4 py-2.5 text-sm font-medium rounded-xl transition-all duration-200 #{ current_page?(nav_item[:path]) ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900 hover:bg-white/60' }",
                    'aria-current': (current_page?(nav_item[:path]) ? 'page' : nil) do %>
                  <svg class="w-4 h-4 transition-transform group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<%= nav_item[:icon] %>"></path>
                  </svg>
                  <span><%= nav_item[:label] %></span>
                  <% if current_page?(nav_item[:path]) %>
                    <div class="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-blue-500 rounded-full"></div>
                  <% end %>
                <% end %>
              <% end %>
            </div>
            
          <% else %>
            <!-- Dashboard Navigation Pills -->
            <div class="flex items-center space-x-2 bg-gray-50/50 backdrop-blur-sm rounded-2xl p-1 border border-gray-200/50">
              <% dashboard_nav_items = [
                   { path: dashboard_path, label: 'Overview', icon: 'M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z', controller: 'dashboard' },
                   { path: campaigns_path, label: 'Campaigns', icon: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z', controller: 'campaigns' },
                   { path: audiences_path, label: 'Audiences', icon: 'M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z', controller: 'audiences' },
                   { path: ai_agents_path, label: 'AI Agents', icon: 'M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z', controller: 'ai_agents', badge: 'AI' }
                 ] %>
              
              <% dashboard_nav_items.each do |nav_item| %>
                <% is_active = (nav_item[:controller] && controller_name == nav_item[:controller]) || current_page?(nav_item[:path]) %>
                <%= link_to nav_item[:path], 
                    class: "group relative flex items-center space-x-2 px-4 py-2.5 text-sm font-medium rounded-xl transition-all duration-200 #{ is_active ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900 hover:bg-white/60' }",
                    'aria-current': (is_active ? 'page' : nil) do %>
                  <svg class="w-4 h-4 transition-transform group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<%= nav_item[:icon] %>"></path>
                  </svg>
                  <span><%= nav_item[:label] %></span>
                  <% if nav_item[:badge] %>
                    <span class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-gradient-to-r from-blue-500 to-purple-500 text-white">
                      <%= nav_item[:badge] %>
                    </span>
                  <% end %>
                  <% if is_active %>
                    <div class="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-blue-500 rounded-full"></div>
                  <% end %>
                <% end %>
              <% end %>
              
              <!-- Analytics with Special Styling -->
              <%= link_to vibe_analytics_path,
                  class: "group relative flex items-center space-x-2 px-4 py-2.5 text-sm font-medium rounded-xl transition-all duration-200 #{ controller_name == 'vibe_analytics' ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg shadow-purple-500/25' : 'text-gray-600 hover:text-purple-700 hover:bg-purple-50' }",
                  'aria-current': (controller_name == 'vibe_analytics' ? 'page' : nil) do %>
                <svg class="w-4 h-4 transition-transform group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                </svg>
                <span>Vibe</span>
                <% if controller_name == 'vibe_analytics' %>
                  <div class="absolute -top-1 -right-1 w-2 h-2 bg-white rounded-full animate-pulse"></div>
                <% else %>
                  <span class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-gradient-to-r from-purple-500 to-pink-500 text-white animate-pulse">
                    Live
                  </span>
                <% end %>
              <% end %>
            </div>
          <% end %>
        </div>
        
        <!-- Right Section -->
        <div class="flex items-center space-x-4">
          <% if user_signed_in? %>
            <!-- AI Status Indicator -->
            <div class="hidden lg:flex items-center space-x-2 px-3 py-2 bg-green-50/80 backdrop-blur-sm rounded-xl border border-green-200/50">
              <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span class="text-xs font-medium text-green-700">AI Active</span>
            </div>
            
            <!-- Notifications -->
            <button type="button" 
                    class="relative p-2.5 bg-gray-50/80 backdrop-blur-sm rounded-xl border border-gray-200/50 text-gray-600 hover:text-gray-900 hover:bg-white/80 transition-all duration-200 hover:scale-105"
                    aria-label="Notifications">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zm-8-4l2-4-2-4H2l2 4-2 4h5z"></path>
              </svg>
              <!-- Notification Badge -->
              <div class="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full border-2 border-white animate-pulse"></div>
            </button>
            
            <!-- User Profile Dropdown -->
            <div class="relative" data-controller="dropdown">
              <button type="button" 
                      class="flex items-center space-x-3 p-2 bg-gray-50/80 backdrop-blur-sm rounded-xl border border-gray-200/50 hover:bg-white/80 transition-all duration-200 hover:scale-105"
                      data-action="click->dropdown#toggle"
                      data-dropdown-target="button"
                      aria-expanded="false"
                      aria-haspopup="true"
                      aria-label="User menu">
                
                <!-- User Avatar -->
                <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg flex items-center justify-center text-white font-semibold text-sm">
                  <%= current_user.first_name.first %><%= current_user.last_name.first %>
                </div>
                
                <!-- User Info (Desktop) -->
                <div class="hidden xl:block text-left">
                  <p class="text-sm font-semibold text-gray-900 leading-tight">
                    <%= current_user.first_name %>
                  </p>
                  <p class="text-xs text-gray-500 capitalize leading-tight">
                    <%= current_user.role %>
                  </p>
                </div>
                
                <!-- Dropdown Arrow -->
                <svg class="w-4 h-4 text-gray-400 transition-transform duration-200"
                     data-dropdown-target="arrow"
                     fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
              </button>
              
              <!-- Dropdown Menu -->
              <div class="absolute right-0 mt-3 w-72 bg-white/95 backdrop-blur-xl rounded-2xl shadow-xl border border-gray-200/50 opacity-0 invisible transform scale-95 transition-all duration-200 z-50"
                   data-dropdown-target="menu"
                   role="menu"
                   aria-orientation="vertical">
                
                <!-- User Info Header -->
                <div class="px-6 py-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-t-2xl border-b border-gray-100/50">
                  <div class="flex items-center space-x-4">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl flex items-center justify-center text-white font-bold">
                      <%= current_user.first_name.first %><%= current_user.last_name.first %>
                    </div>
                    <div class="flex-1 min-w-0">
                      <p class="text-base font-semibold text-gray-900 truncate">
                        <%= current_user.first_name %> <%= current_user.last_name %>
                      </p>
                      <p class="text-sm text-gray-500 truncate">
                        <%= current_user.email %>
                      </p>
                      <div class="inline-flex items-center px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full mt-1">
                        <%= current_user.role.humanize %>
                      </div>
                    </div>
                  </div>
                </div>
                
                <!-- Menu Items -->
                <div class="py-2">
                  <% [
                       { path: profile_path, label: 'Profile Settings', icon: 'M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z', color: 'blue' },
                       { path: account_settings_path, label: 'Account Settings', icon: 'M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z M15 12a3 3 0 11-6 0 3 3 0 016 0z', color: 'purple' },
                       { path: help_index_path, label: 'Help & Support', icon: 'M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z', color: 'green' }
                     ].each do |menu_item| %>
                    <%= link_to menu_item[:path], 
                        class: "group flex items-center space-x-3 px-6 py-3 text-gray-700 hover:bg-gray-50 hover:text-gray-900 transition-all duration-200",
                        role: "menuitem" do %>
                      <div class="w-8 h-8 bg-<%= menu_item[:color] %>-100 rounded-lg flex items-center justify-center group-hover:bg-<%= menu_item[:color] %>-200 transition-colors">
                        <svg class="w-4 h-4 text-<%= menu_item[:color] %>-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<%= menu_item[:icon] %>"></path>
                        </svg>
                      </div>
                      <span class="font-medium"><%= menu_item[:label] %></span>
                    <% end %>
                  <% end %>
                  
                  <!-- Divider -->
                  <div class="border-t border-gray-100 my-3 mx-6"></div>
                  
                  <!-- Sign Out -->
                  <%= button_to destroy_user_session_path,
                      method: :delete,
                      class: "group w-full flex items-center space-x-3 px-6 py-3 text-red-600 hover:bg-red-50 hover:text-red-700 transition-all duration-200",
                      data: { turbo_confirm: "Are you sure you want to sign out?" },
                      role: "menuitem" do %>
                    <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center group-hover:bg-red-200 transition-colors">
                      <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                      </svg>
                    </div>
                    <span class="font-medium">Sign Out</span>
                  <% end %>
                </div>
              </div>
            </div>
            
          <% else %>
            <!-- Guest User Actions -->
            <div class="flex items-center space-x-3">
              <%= link_to "Sign In", new_user_session_path, 
                  class: "px-4 py-2.5 text-sm font-medium text-gray-700 hover:text-gray-900 bg-gray-50/80 backdrop-blur-sm rounded-xl border border-gray-200/50 hover:bg-white/80 transition-all duration-200 hover:scale-105" %>
              
              <%= link_to new_user_registration_path, 
                  class: "group relative px-6 py-2.5 text-sm font-semibold text-white bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 rounded-xl shadow-lg shadow-blue-500/25 hover:shadow-blue-500/40 transform hover:scale-105 transition-all duration-200 overflow-hidden" do %>
                <!-- Animated background -->
                <div class="absolute inset-0 bg-gradient-to-r from-blue-700 via-purple-700 to-pink-700 opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
                <span class="relative flex items-center space-x-2">
                  <span>Start Free Trial</span>
                  <svg class="w-4 h-4 transition-transform group-hover:translate-x-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                  </svg>
                </span>
              <% end %>
            </div>
          <% end %>
          
          <!-- Mobile Menu Button -->
          <button type="button" 
                  class="lg:hidden p-2.5 bg-gray-50/80 backdrop-blur-sm rounded-xl border border-gray-200/50 text-gray-600 hover:text-gray-900 hover:bg-white/80 transition-all duration-200 hover:scale-105"
                  data-action="click->modern-navbar#toggleMobile"
                  data-modern-navbar-target="mobileButton"
                  aria-expanded="false"
                  aria-controls="mobile-menu"
                  aria-label="Toggle mobile menu">
            <svg class="w-5 h-5" data-modern-navbar-target="mobileIcon" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
            <svg class="w-5 h-5 hidden" data-modern-navbar-target="mobileCloseIcon" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Modern Mobile Menu -->
  <div class="lg:hidden absolute top-full left-0 right-0 mt-4 mx-4 opacity-0 invisible transform -translate-y-4 transition-all duration-300"
       data-modern-navbar-target="mobileMenu"
       id="mobile-menu">
    
    <div class="bg-white/95 backdrop-blur-xl rounded-2xl shadow-xl border border-gray-200/50 overflow-hidden">
      <div class="px-6 py-6 space-y-4">
        <% unless user_signed_in? %>
          <!-- Marketing Navigation -->
          <div class="space-y-2">
            <% [
                 { path: features_path, label: 'Features', icon: 'M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10' },
                 { path: pricing_path, label: 'Pricing', icon: 'M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z' },
                 { path: about_path, label: 'About', icon: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z' },
                 { path: contact_path, label: 'Contact', icon: 'M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z' }
               ].each do |nav_item| %>
              <%= link_to nav_item[:path], 
                  class: "group flex items-center space-x-3 p-4 rounded-xl hover:bg-gray-50 transition-all duration-200 #{ current_page?(nav_item[:path]) ? 'bg-blue-50 text-blue-600' : 'text-gray-700' }" do %>
                <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center group-hover:bg-gray-200 transition-colors">
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<%= nav_item[:icon] %>"></path>
                  </svg>
                </div>
                <span class="font-medium"><%= nav_item[:label] %></span>
              <% end %>
            <% end %>
          </div>
          
          <!-- Mobile Sign Up -->
          <div class="pt-4 border-t border-gray-100">
            <%= link_to new_user_registration_path, 
                class: "block w-full text-center px-6 py-3 text-white bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl font-semibold shadow-lg" do %>
              Start Free Trial
            <% end %>
          </div>
          
        <% else %>
          <!-- Dashboard Navigation for Mobile -->
          <div class="space-y-2">
            <% dashboard_nav_items = [
                 { path: dashboard_path, label: 'Overview', icon: 'M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z', controller: 'dashboard' },
                 { path: campaigns_path, label: 'Campaigns', icon: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z', controller: 'campaigns' },
                 { path: audiences_path, label: 'Audiences', icon: 'M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z', controller: 'audiences' },
                 { path: ai_agents_path, label: 'AI Agents', icon: 'M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z', controller: 'ai_agents' },
                 { path: vibe_analytics_path, label: 'Vibe Analytics', icon: 'M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z', controller: 'vibe_analytics' }
               ] %>
            
            <% dashboard_nav_items.each do |nav_item| %>
              <% is_active = (nav_item[:controller] && controller_name == nav_item[:controller]) || current_page?(nav_item[:path]) %>
              <%= link_to nav_item[:path], 
                  class: "group flex items-center space-x-3 p-4 rounded-xl hover:bg-gray-50 transition-all duration-200 #{ is_active ? 'bg-blue-50 text-blue-600' : 'text-gray-700' }" do %>
                <div class="w-10 h-10 #{ is_active ? 'bg-blue-100' : 'bg-gray-100' } rounded-lg flex items-center justify-center group-hover:bg-gray-200 transition-colors">
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<%= nav_item[:icon] %>"></path>
                  </svg>
                </div>
                <span class="font-medium"><%= nav_item[:label] %></span>
                <% if is_active %>
                  <div class="ml-auto w-2 h-2 bg-blue-500 rounded-full"></div>
                <% end %>
              <% end %>
            <% end %>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</nav>
