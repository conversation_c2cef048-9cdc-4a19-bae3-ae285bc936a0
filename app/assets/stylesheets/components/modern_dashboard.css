/* Modern Dashboard Styles for AI Marketing Hub */
/* Clean, minimal design inspired by modern dashboard patterns */

/* Base Dashboard Layout */
.modern-dashboard {
  background-color: #f9fafb;
  min-height: 100vh;
}

/* Dashboard Container */
.dashboard-container {
  display: flex;
  min-height: 100vh;
}

/* Sidebar Styling */
.dashboard-sidebar {
  position: fixed;
  top: 0;
  left: 0;
  width: 256px; /* 64 * 4 = 256px (w-64) */
  height: 100vh;
  background: white;
  border-right: 1px solid #f3f4f6;
  transition: all 0.2s ease;
  z-index: 30;
  overflow-y: auto;
}

/* Main Content Area */
.dashboard-main {
  flex: 1;
  margin-left: 256px; /* Same as sidebar width */
  min-height: 100vh;
  background-color: #f9fafb;
}

/* Responsive sidebar adjustments */
@media (max-width: 1024px) {
  .dashboard-sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .dashboard-sidebar.open {
    transform: translateX(0);
  }
  
  .dashboard-main {
    margin-left: 0;
  }
}

/* Navigation Item Styling */
.dashboard-sidebar .nav-item {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 12px;
  margin: 2px 0;
}

.dashboard-sidebar .nav-item:hover {
  background-color: #f9fafb;
  transform: translateX(2px);
}

.dashboard-sidebar .nav-item.active {
  background-color: #eff6ff;
  color: #2563eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.dashboard-sidebar .nav-item.active svg {
  color: #2563eb;
}

/* Card Styling */
.dashboard-card {
  background: white;
  border: 1px solid #f3f4f6;
  border-radius: 16px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.dashboard-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

/* Stats Cards */
.stats-card {
  position: relative;
  overflow: hidden;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  border-radius: 16px 16px 0 0;
}

/* Activity Feed */
.activity-item {
  background: #f9fafb;
  border-radius: 12px;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.activity-item:hover {
  background: white;
  border-color: #e5e7eb;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Status Badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: capitalize;
}

.status-badge.completed {
  background-color: #dcfce7;
  color: #166534;
}

.status-badge.running {
  background-color: #dbeafe;
  color: #1e40af;
}

.status-badge.active {
  background-color: #dcfce7;
  color: #166534;
}

.status-badge.offline {
  background-color: #f3f4f6;
  color: #6b7280;
}

/* Dropdown Styling */
.dropdown-menu {
  background: white;
  border: 1px solid #f3f4f6;
  border-radius: 12px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(8px);
  transform-origin: top right;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 50;
}

.dropdown-menu a {
  transition: all 0.15s ease;
  border-radius: 8px;
  margin: 2px 4px;
}

.dropdown-menu a:hover {
  background-color: #f9fafb;
  color: #374151;
}

/* Circular Progress */
.circular-progress {
  transform: rotate(-90deg);
}

.circular-progress circle {
  transition: stroke-dasharray 0.3s ease;
}

/* Avatar Styling */
.user-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  transition: all 0.2s ease;
}

.user-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Campaign Item Styling */
.campaign-item {
  transition: all 0.2s ease;
  border-radius: 12px;
  padding: 16px;
}

.campaign-item:hover {
  background-color: #f9fafb;
  transform: translateX(4px);
}

/* Button Styling */
.btn-primary {
  background: #111827;
  color: white;
  border-radius: 12px;
  transition: all 0.2s ease;
  border: none;
  font-weight: 500;
}

.btn-primary:hover {
  background: #1f2937;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn-secondary {
  background: #f9fafb;
  color: #374151;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  transition: all 0.2s ease;
  font-weight: 500;
}

.btn-secondary:hover {
  background: white;
  border-color: #d1d5db;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Header Styling */
.dashboard-header {
  background: white;
  border-bottom: 1px solid #f3f4f6;
  backdrop-filter: blur(8px);
}

/* Chart Area */
.chart-container {
  background: #f9fafb;
  border-radius: 12px;
  position: relative;
  overflow: hidden;
}

.chart-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
}

/* AI Feature Cards */
.ai-feature-card {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0e7ff 100%);
  border-radius: 12px;
  border: 1px solid #e0e7ff;
  transition: all 0.2s ease;
}

.ai-feature-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

.ai-feature-card.purple {
  background: linear-gradient(135deg, #fdf4ff 0%, #f3e8ff 100%);
  border-color: #e9d5ff;
}

.ai-feature-card.purple:hover {
  box-shadow: 0 8px 25px rgba(147, 51, 234, 0.15);
}

/* Input Styling */
.chat-input {
  background: transparent;
  border: none;
  outline: none;
  color: #374151;
  font-size: 0.875rem;
}

.chat-input::placeholder {
  color: #9ca3af;
}

.chat-send-btn {
  background: #2563eb;
  color: white;
  border-radius: 8px;
  border: none;
  transition: all 0.2s ease;
}

.chat-send-btn:hover {
  background: #1d4ed8;
  transform: scale(1.05);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .dashboard-sidebar {
    transform: translateX(-100%);
    position: fixed;
    z-index: 40;
    height: 100vh;
  }
  
  .dashboard-sidebar.open {
    transform: translateX(0);
  }
  
  .dashboard-content {
    margin-left: 0;
  }
}

/* Loading States */
.skeleton {
  background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 8px;
}

@keyframes skeleton-loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Animations */
.fade-in {
  animation: fadeIn 0.3s ease forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-up {
  animation: slideUp 0.3s ease forwards;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Focus States */
.focus-ring {
  transition: all 0.2s ease;
}

.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

/* Performance Optimizations */
.dashboard-card,
.dropdown-menu,
.activity-item,
.campaign-item {
  will-change: transform;
  backface-visibility: hidden;
}

/* Dark Mode Support (if needed) */
@media (prefers-color-scheme: dark) {
  .modern-dashboard {
    background-color: #111827;
  }
  
  .dashboard-card {
    background: #1f2937;
    border-color: #374151;
  }
  
  .dashboard-sidebar {
    background: #1f2937;
    border-color: #374151;
  }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .dashboard-card {
    border-width: 2px;
    border-color: #000;
  }
  
  .status-badge {
    border: 1px solid;
  }
}
